/**
 * Navigation type definitions for SipTracker app
 */

export type TabParamList = {
  index: undefined;
  search: undefined;
  profile: undefined;
};

export type AuthParamList = {
  login: undefined;
  register: undefined;
};

export type RootParamList = {
  '(tabs)': undefined;
  'auth/login': undefined;
  'auth/register': undefined;
  '+not-found': undefined;
};

// Screen names for type safety
export const SCREEN_NAMES = {
  TABS: {
    EXPLORE: 'index' as const,
    SEARCH: 'search' as const,
    PROFILE: 'profile' as const,
  },
  AUTH: {
    LOGIN: 'auth/login' as const,
    REGISTER: 'auth/register' as const,
  },
} as const;
