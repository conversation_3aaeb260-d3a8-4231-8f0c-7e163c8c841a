import { router } from 'expo-router';
import React from 'react';
import { Alert, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/hooks/useAuth';
import { useColorScheme } from '@/hooks/useColorScheme';
import { signOut } from '@/utils/auth';

export default function ExploreScreen() {
  const { user, loading, isAuthenticated } = useAuth();
  const colorScheme = useColorScheme();

  const handleSignOut = async () => {
    const result = await signOut();
    if (result.success) {
      router.replace('/auth/login');
    } else {
      Alert.alert('Error', result.error || 'Failed to sign out');
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText>Yükleniyor...</ThemedText>
      </ThemedView>
    );
  }

  if (!isAuthenticated) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText type="title">Giriş Gerekli</ThemedText>
        <ThemedText>Bu sayfayı görüntülemek için giriş yapmalısınız.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <ThemedView style={styles.content}>
        {/* Header Section */}
        <ThemedView style={styles.header}>
          <ThemedText type="title">Keşfet</ThemedText>
          <ThemedText style={styles.subtitle}>
            Hoş geldin, {user?.email?.split('@')[0]}!
          </ThemedText>
        </ThemedView>

        {/* Quick Actions */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Hızlı Erişim</ThemedText>
          <ThemedView style={styles.quickActions}>
            <TouchableOpacity
              style={[styles.actionCard, { backgroundColor: Colors[colorScheme ?? 'dark'].card }]}
            >
              <ThemedText style={styles.actionTitle}>Yeni Kayıt</ThemedText>
              <ThemedText style={styles.actionSubtitle}>Hızlı kayıt oluştur</ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionCard, { backgroundColor: Colors[colorScheme ?? 'dark'].card }]}
            >
              <ThemedText style={styles.actionTitle}>Son Kayıtlar</ThemedText>
              <ThemedText style={styles.actionSubtitle}>Geçmiş kayıtları görüntüle</ThemedText>
            </TouchableOpacity>
          </ThemedView>
        </ThemedView>

        {/* Recent Activity */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Son Aktiviteler</ThemedText>
          <ThemedView style={[styles.activityCard, { backgroundColor: Colors[colorScheme ?? 'dark'].card }]}>
            <ThemedText>Henüz aktivite bulunmuyor</ThemedText>
            <ThemedText style={styles.activitySubtext}>
              İlk kaydınızı oluşturarak başlayın
            </ThemedText>
          </ThemedView>
        </ThemedView>

        {/* User Actions */}
        <ThemedView style={styles.section}>
          <TouchableOpacity
            style={[styles.signOutButton, { backgroundColor: Colors[colorScheme ?? 'dark'].error }]}
            onPress={handleSignOut}
          >
            <ThemedText style={styles.signOutText}>Çıkış Yap</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    marginBottom: 30,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginTop: 8,
  },
  section: {
    marginBottom: 30,
  },
  quickActions: {
    flexDirection: 'row',
    gap: 15,
    marginTop: 15,
  },
  actionCard: {
    flex: 1,
    padding: 20,
    borderRadius: 12,
    minHeight: 80,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  actionSubtitle: {
    fontSize: 14,
    opacity: 0.7,
  },
  activityCard: {
    padding: 20,
    borderRadius: 12,
    marginTop: 15,
    alignItems: 'center',
  },
  activitySubtext: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 8,
    textAlign: 'center',
  },
  signOutButton: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  signOutText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
});
