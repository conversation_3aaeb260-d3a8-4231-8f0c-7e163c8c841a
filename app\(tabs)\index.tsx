import { Image } from 'expo-image';
import { Platform, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { router } from 'expo-router';

import { HelloWave } from '@/components/HelloWave';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useAuth } from '@/hooks/useAuth';
import { signOut } from '@/utils/auth';

export default function HomeScreen() {
  const { user, loading, isAuthenticated } = useAuth();

  const handleSignOut = async () => {
    const result = await signOut();
    if (result.success) {
      router.replace('/auth/login');
    } else {
      Alert.alert('Error', result.error || 'Failed to sign out');
    }
  };

  const navigateToAuth = () => {
    router.push('/auth/login');
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText>Loading...</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
      headerImage={
        <Image
          source={require('@/assets/images/partial-react-logo.png')}
          style={styles.reactLogo}
        />
      }>
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">Welcome!</ThemedText>
        <HelloWave />
      </ThemedView>

      {/* Authentication Status Section */}
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Authentication Status</ThemedText>
        {isAuthenticated ? (
          <ThemedView>
            <ThemedText>
              ✅ Signed in as: <ThemedText type="defaultSemiBold">{user?.email}</ThemedText>
            </ThemedText>
            <TouchableOpacity style={styles.authButton} onPress={handleSignOut}>
              <ThemedText style={styles.authButtonText}>Sign Out</ThemedText>
            </TouchableOpacity>
          </ThemedView>
        ) : (
          <ThemedView>
            <ThemedText>❌ Not signed in</ThemedText>
            <TouchableOpacity style={styles.authButton} onPress={navigateToAuth}>
              <ThemedText style={styles.authButtonText}>Sign In</ThemedText>
            </TouchableOpacity>
          </ThemedView>
        )}
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 1: Configure Supabase</ThemedText>
        <ThemedText>
          Update your <ThemedText type="defaultSemiBold">.env</ThemedText> file with your Supabase credentials.
          You can find these in your Supabase project settings.
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 2: Test Authentication</ThemedText>
        <ThemedText>
          Use the authentication buttons above to test the login and registration flow.
          The app is now configured with Supabase integration!
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 3: Build Your App</ThemedText>
        <ThemedText>
          You now have a solid foundation with:
        </ThemedText>
        <ThemedText>• Expo Router navigation</ThemedText>
        <ThemedText>• Supabase authentication</ThemedText>
        <ThemedText>• TypeScript support</ThemedText>
        <ThemedText>• Organized folder structure</ThemedText>
      </ThemedView>
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stepContainer: {
    gap: 8,
    marginBottom: 8,
  },
  reactLogo: {
    height: 178,
    width: 290,
    bottom: 0,
    left: 0,
    position: 'absolute',
  },
  authButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginTop: 8,
    maxWidth: 120,
  },
  authButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
});
