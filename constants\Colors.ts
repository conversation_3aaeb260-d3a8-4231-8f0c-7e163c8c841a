/**
 * SipTracker Color Palette
 * Dark-focused design system with warm brown accents
 */

export const Colors = {
  light: {
    text: '#111827',
    background: '#fff',
    tint: '#0a7ea4',
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: '#0a7ea4',
    card: '#f9f9f9',
    primary: '#0a7ea4',
    secondary: '#6b7280',
    accent: '#3b82f6',
    border: '#e5e7eb',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
  },
  dark: {
    background: '#121212',        // Main dark background
    card: '#1E1E1E',             // Cards and components
    text: '#EFEFEF',             // Light text
    primary: '#A67B5B',          // Main brown (buttons, highlights)
    secondary: '#D4A57A',        // Secondary lighter brown
    accent: '#E8D5C4',           // Accent color (beige/cream)
    icon: '#EFEFEF',             // Icon color
    tabIconDefault: '#888888',   // Inactive tab icon
    tabIconSelected: '#A67B5B',  // Active tab icon
    tint: '#A67B5B',
    border: '#2d2d2d',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
  },
};
