import React from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useAuth } from '@/hooks/useAuth';
import { signOut } from '@/utils/auth';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function ProfileScreen() {
  const { user, loading, isAuthenticated } = useAuth();
  const colorScheme = useColorScheme();

  const handleSignOut = async () => {
    Alert.alert(
      'Çıkış Yap',
      'Hesabınızdan çıkmak istediğinizden emin misiniz?',
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: '<PERSON><PERSON>k<PERSON><PERSON> Yap',
          style: 'destructive',
          onPress: async () => {
            const result = await signOut();
            if (result.success) {
              router.replace('/auth/login');
            } else {
              Alert.alert('Hata', result.error || '<PERSON>ık<PERSON>ş yapılamadı');
            }
          },
        },
      ]
    );
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText>Yükleniyor...</ThemedText>
      </ThemedView>
    );
  }

  if (!isAuthenticated) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText type="title">Giriş Gerekli</ThemedText>
        <ThemedText>Bu sayfayı görüntülemek için giriş yapmalısınız.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <ThemedView style={styles.content}>
        {/* Header */}
        <ThemedView style={styles.header}>
          <ThemedText type="title">Profil</ThemedText>
          <ThemedText style={styles.subtitle}>
            Hesap ayarlarınızı yönetin
          </ThemedText>
        </ThemedView>

        {/* User Info Card */}
        <ThemedView style={[
          styles.userCard,
          { backgroundColor: Colors[colorScheme ?? 'dark'].card }
        ]}>
          <ThemedView style={styles.userInfo}>
            <ThemedView style={[
              styles.avatar,
              { backgroundColor: Colors[colorScheme ?? 'dark'].primary }
            ]}>
              <ThemedText style={styles.avatarText}>
                {user?.email?.charAt(0).toUpperCase() || 'U'}
              </ThemedText>
            </ThemedView>
            <ThemedView style={styles.userDetails}>
              <ThemedText style={styles.userName}>
                {user?.email?.split('@')[0] || 'Kullanıcı'}
              </ThemedText>
              <ThemedText style={styles.userEmail}>
                {user?.email || '<EMAIL>'}
              </ThemedText>
            </ThemedView>
          </ThemedView>
        </ThemedView>

        {/* Settings Section */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Ayarlar</ThemedText>
          
          <TouchableOpacity style={[
            styles.settingItem,
            { backgroundColor: Colors[colorScheme ?? 'dark'].card }
          ]}>
            <ThemedText style={styles.settingText}>Hesap Bilgileri</ThemedText>
            <ThemedText style={styles.settingArrow}>›</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity style={[
            styles.settingItem,
            { backgroundColor: Colors[colorScheme ?? 'dark'].card }
          ]}>
            <ThemedText style={styles.settingText}>Bildirimler</ThemedText>
            <ThemedText style={styles.settingArrow}>›</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity style={[
            styles.settingItem,
            { backgroundColor: Colors[colorScheme ?? 'dark'].card }
          ]}>
            <ThemedText style={styles.settingText}>Gizlilik</ThemedText>
            <ThemedText style={styles.settingArrow}>›</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity style={[
            styles.settingItem,
            { backgroundColor: Colors[colorScheme ?? 'dark'].card }
          ]}>
            <ThemedText style={styles.settingText}>Yardım & Destek</ThemedText>
            <ThemedText style={styles.settingArrow}>›</ThemedText>
          </TouchableOpacity>
        </ThemedView>

        {/* Statistics Section */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">İstatistikler</ThemedText>
          
          <ThemedView style={styles.statsContainer}>
            <ThemedView style={[
              styles.statCard,
              { backgroundColor: Colors[colorScheme ?? 'dark'].card }
            ]}>
              <ThemedText style={styles.statNumber}>0</ThemedText>
              <ThemedText style={styles.statLabel}>Toplam Kayıt</ThemedText>
            </ThemedView>

            <ThemedView style={[
              styles.statCard,
              { backgroundColor: Colors[colorScheme ?? 'dark'].card }
            ]}>
              <ThemedText style={styles.statNumber}>0</ThemedText>
              <ThemedText style={styles.statLabel}>Bu Ay</ThemedText>
            </ThemedView>
          </ThemedView>
        </ThemedView>

        {/* Sign Out Button */}
        <ThemedView style={styles.section}>
          <TouchableOpacity 
            style={[
              styles.signOutButton,
              { backgroundColor: Colors[colorScheme ?? 'dark'].error }
            ]}
            onPress={handleSignOut}
          >
            <ThemedText style={styles.signOutText}>Çıkış Yap</ThemedText>
          </TouchableOpacity>
        </ThemedView>

        {/* App Info */}
        <ThemedView style={styles.appInfo}>
          <ThemedText style={styles.appInfoText}>
            SipTracker v1.0.0
          </ThemedText>
          <ThemedText style={styles.appInfoText}>
            © 2024 SipTracker
          </ThemedText>
        </ThemedView>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    marginBottom: 30,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginTop: 8,
  },
  userCard: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 30,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  avatarText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    opacity: 0.7,
  },
  section: {
    marginBottom: 30,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderRadius: 8,
    marginTop: 8,
  },
  settingText: {
    fontSize: 16,
  },
  settingArrow: {
    fontSize: 20,
    opacity: 0.5,
  },
  statsContainer: {
    flexDirection: 'row',
    gap: 15,
    marginTop: 15,
  },
  statCard: {
    flex: 1,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    opacity: 0.7,
  },
  signOutButton: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  signOutText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  appInfo: {
    alignItems: 'center',
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  appInfoText: {
    fontSize: 12,
    opacity: 0.5,
    marginBottom: 4,
  },
});
