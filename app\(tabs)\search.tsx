import React, { useState } from 'react';
import { StyleSheet, ScrollView, TextInput, TouchableOpacity } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useAuth } from '@/hooks/useAuth';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function SearchScreen() {
  const { user, loading, isAuthenticated } = useAuth();
  const colorScheme = useColorScheme();
  const [searchQuery, setSearchQuery] = useState('');

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText>Yükleniyor...</ThemedText>
      </ThemedView>
    );
  }

  if (!isAuthenticated) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText type="title"><PERSON><PERSON><PERSON></ThemedText>
        <ThemedText>Bu sayfayı görüntülemek için giriş yapmalısınız.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <ThemedView style={styles.content}>
        {/* Header */}
        <ThemedView style={styles.header}>
          <ThemedText type="title">Ara</ThemedText>
          <ThemedText style={styles.subtitle}>
            Kayıtlarınızda arama yapın
          </ThemedText>
        </ThemedView>

        {/* Search Input */}
        <ThemedView style={styles.searchSection}>
          <TextInput
            style={[
              styles.searchInput,
              {
                backgroundColor: Colors[colorScheme ?? 'dark'].card,
                color: Colors[colorScheme ?? 'dark'].text,
                borderColor: Colors[colorScheme ?? 'dark'].border,
              }
            ]}
            placeholder="Arama yapın..."
            placeholderTextColor={Colors[colorScheme ?? 'dark'].icon}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          
          <TouchableOpacity 
            style={[
              styles.searchButton,
              { backgroundColor: Colors[colorScheme ?? 'dark'].primary }
            ]}
          >
            <ThemedText style={styles.searchButtonText}>Ara</ThemedText>
          </TouchableOpacity>
        </ThemedView>

        {/* Search Filters */}
        <ThemedView style={styles.filtersSection}>
          <ThemedText type="subtitle">Filtreler</ThemedText>
          <ThemedView style={styles.filterButtons}>
            <TouchableOpacity 
              style={[
                styles.filterButton,
                { backgroundColor: Colors[colorScheme ?? 'dark'].card }
              ]}
            >
              <ThemedText style={styles.filterButtonText}>Tümü</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.filterButton,
                { backgroundColor: Colors[colorScheme ?? 'dark'].card }
              ]}
            >
              <ThemedText style={styles.filterButtonText}>Bu Hafta</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.filterButton,
                { backgroundColor: Colors[colorScheme ?? 'dark'].card }
              ]}
            >
              <ThemedText style={styles.filterButtonText}>Bu Ay</ThemedText>
            </TouchableOpacity>
          </ThemedView>
        </ThemedView>

        {/* Search Results */}
        <ThemedView style={styles.resultsSection}>
          <ThemedText type="subtitle">Sonuçlar</ThemedText>
          <ThemedView style={[
            styles.emptyState,
            { backgroundColor: Colors[colorScheme ?? 'dark'].card }
          ]}>
            <ThemedText style={styles.emptyStateText}>
              {searchQuery ? 'Arama sonucu bulunamadı' : 'Arama yapmak için yukarıdaki kutuyu kullanın'}
            </ThemedText>
            <ThemedText style={styles.emptyStateSubtext}>
              {searchQuery ? 'Farklı anahtar kelimeler deneyin' : 'Kayıtlarınızda hızlı arama yapabilirsiniz'}
            </ThemedText>
          </ThemedView>
        </ThemedView>

        {/* Recent Searches */}
        <ThemedView style={styles.recentSection}>
          <ThemedText type="subtitle">Son Aramalar</ThemedText>
          <ThemedView style={[
            styles.recentCard,
            { backgroundColor: Colors[colorScheme ?? 'dark'].card }
          ]}>
            <ThemedText style={styles.recentText}>
              Henüz arama geçmişi bulunmuyor
            </ThemedText>
          </ThemedView>
        </ThemedView>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    marginBottom: 30,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginTop: 8,
  },
  searchSection: {
    marginBottom: 30,
  },
  searchInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 15,
    fontSize: 16,
    marginBottom: 15,
  },
  searchButton: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  searchButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  filtersSection: {
    marginBottom: 30,
  },
  filterButtons: {
    flexDirection: 'row',
    gap: 10,
    marginTop: 15,
    flexWrap: 'wrap',
  },
  filterButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  resultsSection: {
    marginBottom: 30,
  },
  emptyState: {
    padding: 30,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 15,
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
  },
  recentSection: {
    marginBottom: 30,
  },
  recentCard: {
    padding: 20,
    borderRadius: 12,
    marginTop: 15,
  },
  recentText: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
  },
});
